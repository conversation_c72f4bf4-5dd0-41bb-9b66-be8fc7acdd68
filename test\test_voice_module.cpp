#include <unity.h>
#include <Arduino.h>
#include <Audio.h>
#include <LittleFS.h>

// Include voice module headers
#include "../src/voice_module/voice_embedded.h"
#include "../src/voice_module/voice_manager.h"
#include "../src/voice_module/voice_player.h"
#include "../src/voice_module/voice_interface.h"

// Mock Audio instance for testing
Audio test_audio;

// Test setup and teardown
void setUp(void) {
    // Initialize LittleFS for temporary file operations
    if (!LittleFS.begin(true)) {
        TEST_FAIL_MESSAGE("Failed to initialize Little<PERSON>");
    }
}

void tearDown(void) {
    // Clean up any temporary files
    File root = LittleFS.open("/");
    File file = root.openNextFile();
    while (file) {
        String filename = file.name();
        file.close();
        if (filename.startsWith("/tmp_")) {
            LittleFS.remove(filename);
        }
        file = root.openNextFile();
    }
    root.close();
}

// Test voice_embedded module
void test_voice_embedded_get_pointers(void) {
    // Test getting start and end pointers for valid indices
    for (uint8_t i = 0; i < 10; i++) {
        const uint8_t* start = voice_embedded_get_start_ptr(i);
        const uint8_t* end = voice_embedded_get_end_ptr(i);
        
        // Pointers should not be null (assuming files are embedded)
        TEST_ASSERT_NOT_NULL_MESSAGE(start, "Start pointer should not be null");
        TEST_ASSERT_NOT_NULL_MESSAGE(end, "End pointer should not be null");
        TEST_ASSERT_TRUE_MESSAGE(end > start, "End pointer should be greater than start");
    }
}

void test_voice_embedded_invalid_index(void) {
    // Test invalid indices
    TEST_ASSERT_NULL(voice_embedded_get_start_ptr(10));
    TEST_ASSERT_NULL(voice_embedded_get_end_ptr(10));
    TEST_ASSERT_EQUAL(0, voice_embedded_get_size(10));
    TEST_ASSERT_FALSE(voice_embedded_is_valid(10));
}

void test_voice_embedded_size_calculation(void) {
    for (uint8_t i = 0; i < 10; i++) {
        size_t size = voice_embedded_get_size(i);
        const uint8_t* start = voice_embedded_get_start_ptr(i);
        const uint8_t* end = voice_embedded_get_end_ptr(i);
        
        if (start != nullptr && end != nullptr && end > start) {
            size_t expected_size = (size_t)(end - start);
            TEST_ASSERT_EQUAL_MESSAGE(expected_size, size, "Size calculation should match pointer difference");
        }
    }
}

// Test voice_manager module
void test_voice_manager_init_deinit(void) {
    // Test initialization
    voice_error_t result = voice_manager_init();
    TEST_ASSERT_EQUAL_MESSAGE(VOICE_ERROR_NONE, result, "Voice manager should initialize successfully");
    
    // Test setting audio instance
    result = voice_manager_set_audio_instance(&test_audio);
    TEST_ASSERT_EQUAL_MESSAGE(VOICE_ERROR_NONE, result, "Should set audio instance successfully");
    
    // Test deinitialization
    voice_manager_deinit();
    
    // Test double initialization
    result = voice_manager_init();
    TEST_ASSERT_EQUAL_MESSAGE(VOICE_ERROR_NONE, result, "Should handle re-initialization");
    
    voice_manager_deinit();
}

void test_voice_manager_file_info(void) {
    voice_manager_init();
    
    // Test file count
    uint8_t count = voice_manager_get_file_count();
    TEST_ASSERT_EQUAL_MESSAGE(10, count, "Should have 10 voice files");
    
    // Test file availability
    for (uint8_t i = 0; i < count; i++) {
        bool available = voice_manager_is_file_available((voice_file_index_t)i);
        const voice_file_t* info = voice_manager_get_file_info((voice_file_index_t)i);
        
        TEST_ASSERT_NOT_NULL_MESSAGE(info, "File info should not be null");
        TEST_ASSERT_NOT_NULL_MESSAGE(info->name, "File name should not be null");
        
        if (available) {
            TEST_ASSERT_NOT_NULL_MESSAGE(info->data, "Available file should have data");
            TEST_ASSERT_TRUE_MESSAGE(info->size > 0, "Available file should have size > 0");
        }
    }
    
    voice_manager_deinit();
}

void test_voice_manager_error_strings(void) {
    // Test all error codes have valid strings
    const char* error_str;
    
    error_str = voice_manager_error_to_string(VOICE_ERROR_NONE);
    TEST_ASSERT_NOT_NULL(error_str);
    TEST_ASSERT_TRUE(strlen(error_str) > 0);
    
    error_str = voice_manager_error_to_string(VOICE_ERROR_NOT_INITIALIZED);
    TEST_ASSERT_NOT_NULL(error_str);
    TEST_ASSERT_TRUE(strlen(error_str) > 0);
    
    error_str = voice_manager_error_to_string(VOICE_ERROR_INVALID_INDEX);
    TEST_ASSERT_NOT_NULL(error_str);
    TEST_ASSERT_TRUE(strlen(error_str) > 0);
}

// Test voice_player module
void test_voice_player_init_deinit(void) {
    // Test initialization
    bool result = voice_player_init(&test_audio);
    TEST_ASSERT_TRUE_MESSAGE(result, "Voice player should initialize successfully");
    
    // Test status
    voice_player_status_t status = voice_player_get_status();
    TEST_ASSERT_EQUAL_MESSAGE(VOICE_PLAYER_IDLE, status, "Initial status should be idle");
    
    // Test deinitialization
    voice_player_deinit();
    
    // Test null audio instance
    result = voice_player_init(nullptr);
    TEST_ASSERT_FALSE_MESSAGE(result, "Should fail with null audio instance");
}

void test_voice_player_volume_control(void) {
    voice_player_init(&test_audio);
    
    // Test volume setting
    voice_player_set_volume(15);
    uint8_t volume = voice_player_get_volume();
    TEST_ASSERT_EQUAL_MESSAGE(15, volume, "Volume should be set correctly");
    
    // Test volume limits
    voice_player_set_volume(25); // Above max
    volume = voice_player_get_volume();
    TEST_ASSERT_EQUAL_MESSAGE(21, volume, "Volume should be clamped to max");
    
    voice_player_deinit();
}

// Test voice_interface module
void test_voice_interface_init_deinit(void) {
    // Test initialization
    bool result = voice_system_init(&test_audio);
    TEST_ASSERT_TRUE_MESSAGE(result, "Voice system should initialize successfully");
    
    // Test file count
    uint8_t count = voice_system_get_file_count();
    TEST_ASSERT_EQUAL_MESSAGE(10, count, "Should have 10 voice files");
    
    // Test deinitialization
    voice_system_deinit();
    
    // Test null audio instance
    result = voice_system_init(nullptr);
    TEST_ASSERT_FALSE_MESSAGE(result, "Should fail with null audio instance");
}

void test_voice_interface_availability_check(void) {
    voice_system_init(&test_audio);
    
    // Test valid indices
    for (uint8_t i = 0; i < 10; i++) {
        bool available = voice_system_is_available(i);
        // We can't guarantee files are available without actual embedded data
        // but the function should not crash
        TEST_ASSERT_TRUE_MESSAGE(available == true || available == false, "Should return valid boolean");
    }
    
    // Test invalid index
    bool available = voice_system_is_available(10);
    TEST_ASSERT_FALSE_MESSAGE(available, "Invalid index should return false");
    
    voice_system_deinit();
}

// Integration tests
void test_integration_full_workflow(void) {
    // Test complete workflow
    bool result = voice_system_init(&test_audio);
    TEST_ASSERT_TRUE_MESSAGE(result, "System should initialize");
    
    // Test playing by index (if files are available)
    if (voice_system_is_available(0)) {
        result = voice_system_play_by_index(0);
        // Note: This might fail in test environment without actual audio hardware
        // but should not crash
    }
    
    // Test playing by name
    result = voice_system_play_prompt("network_success");
    // Again, might fail without hardware but should not crash
    
    // Test system status
    bool playing = voice_system_is_playing();
    TEST_ASSERT_TRUE_MESSAGE(playing == true || playing == false, "Should return valid status");
    
    // Test stop
    result = voice_system_stop();
    TEST_ASSERT_TRUE_MESSAGE(result, "Should stop successfully");
    
    // Test process function (should not crash)
    voice_system_process();
    
    voice_system_deinit();
}

// Test runner
void runTests(void) {
    UNITY_BEGIN();
    
    // Voice embedded tests
    RUN_TEST(test_voice_embedded_get_pointers);
    RUN_TEST(test_voice_embedded_invalid_index);
    RUN_TEST(test_voice_embedded_size_calculation);
    
    // Voice manager tests
    RUN_TEST(test_voice_manager_init_deinit);
    RUN_TEST(test_voice_manager_file_info);
    RUN_TEST(test_voice_manager_error_strings);
    
    // Voice player tests
    RUN_TEST(test_voice_player_init_deinit);
    RUN_TEST(test_voice_player_volume_control);
    
    // Voice interface tests
    RUN_TEST(test_voice_interface_init_deinit);
    RUN_TEST(test_voice_interface_availability_check);
    
    // Integration tests
    RUN_TEST(test_integration_full_workflow);
    
    UNITY_END();
}

void setup() {
    delay(2000); // Wait for serial monitor
    runTests();
}

void loop() {
    // Empty loop
}
