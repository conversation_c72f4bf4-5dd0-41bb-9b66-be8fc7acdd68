#include "voice_memory.h"
#include <Arduino.h>
#include <esp_heap_caps.h>

// Global memory pool instance
static voice_memory_pool_t g_voice_memory_pool = {0};

bool voice_memory_init(uint8_t max_buffers, size_t max_memory)
{
    if (g_voice_memory_pool.buffers != nullptr)
    {
        // Already initialized
        return false;
    }

    if (max_buffers == 0 || max_memory == 0)
    {
        return false;
    }

    // Allocate buffer array
    g_voice_memory_pool.buffers = (voice_memory_buffer_t *)malloc(
        sizeof(voice_memory_buffer_t) * max_buffers);

    if (g_voice_memory_pool.buffers == nullptr)
    {
        return false;
    }

    // Initialize buffer array
    for (uint8_t i = 0; i < max_buffers; i++)
    {
        g_voice_memory_pool.buffers[i].data = nullptr;
        g_voice_memory_pool.buffers[i].size = 0;
        g_voice_memory_pool.buffers[i].used = 0;
        g_voice_memory_pool.buffers[i].is_allocated = false;
        g_voice_memory_pool.buffers[i].is_psram = false;
    }

    g_voice_memory_pool.buffer_count = 0;
    g_voice_memory_pool.max_buffers = max_buffers;
    g_voice_memory_pool.total_allocated = 0;
    g_voice_memory_pool.max_memory = max_memory;

    log_i("Voice memory pool initialized: max_buffers=%d, max_memory=%zu",
          max_buffers, max_memory);

    return true;
}

void voice_memory_deinit(void)
{
    if (g_voice_memory_pool.buffers == nullptr)
    {
        return;
    }

    // Free all allocated buffers
    for (uint8_t i = 0; i < g_voice_memory_pool.max_buffers; i++)
    {
        if (g_voice_memory_pool.buffers[i].is_allocated &&
            g_voice_memory_pool.buffers[i].data != nullptr)
        {

            if (g_voice_memory_pool.buffers[i].is_psram)
            {
                heap_caps_free(g_voice_memory_pool.buffers[i].data);
            }
            else
            {
                free(g_voice_memory_pool.buffers[i].data);
            }
        }
    }

    // Free buffer array
    free(g_voice_memory_pool.buffers);

    // Reset pool
    memset(&g_voice_memory_pool, 0, sizeof(voice_memory_pool_t));

    log_i("Voice memory pool deinitialized");
}

voice_memory_buffer_t *voice_memory_allocate(size_t size, bool use_psram)
{
    if (g_voice_memory_pool.buffers == nullptr || size == 0)
    {
        return nullptr;
    }

    // Check if we can allocate
    if (!voice_memory_can_allocate(size))
    {
        log_e("Cannot allocate %zu bytes: would exceed limits", size);
        return nullptr;
    }

    // Find free buffer slot
    voice_memory_buffer_t *buffer = nullptr;
    for (uint8_t i = 0; i < g_voice_memory_pool.max_buffers; i++)
    {
        if (!g_voice_memory_pool.buffers[i].is_allocated)
        {
            buffer = &g_voice_memory_pool.buffers[i];
            break;
        }
    }

    if (buffer == nullptr)
    {
        log_e("No free buffer slots available");
        return nullptr;
    }

    // Allocate memory
    uint8_t *data = nullptr;
    if (use_psram && heap_caps_get_free_size(MALLOC_CAP_SPIRAM) >= size)
    {
        data = (uint8_t *)heap_caps_malloc(size, MALLOC_CAP_SPIRAM);
        buffer->is_psram = true;
    }
    else
    {
        data = (uint8_t *)malloc(size);
        buffer->is_psram = false;
    }

    if (data == nullptr)
    {
        log_e("Failed to allocate %zu bytes", size);
        return nullptr;
    }

    // Initialize buffer
    buffer->data = data;
    buffer->size = size;
    buffer->used = 0;
    buffer->is_allocated = true;

    // Update pool statistics
    g_voice_memory_pool.buffer_count++;
    g_voice_memory_pool.total_allocated += size;

    log_d("Allocated voice buffer: %zu bytes (%s)",
          size, buffer->is_psram ? "PSRAM" : "DRAM");

    return buffer;
}

void voice_memory_free(voice_memory_buffer_t *buffer)
{
    if (buffer == nullptr || !buffer->is_allocated)
    {
        return;
    }

    if (buffer->data != nullptr)
    {
        if (buffer->is_psram)
        {
            heap_caps_free(buffer->data);
        }
        else
        {
            free(buffer->data);
        }

        // Update pool statistics
        g_voice_memory_pool.total_allocated -= buffer->size;
        g_voice_memory_pool.buffer_count--;

        log_d("Freed voice buffer: %zu bytes", buffer->size);
    }

    // Reset buffer
    buffer->data = nullptr;
    buffer->size = 0;
    buffer->used = 0;
    buffer->is_allocated = false;
    buffer->is_psram = false;
}

bool voice_memory_copy_data(voice_memory_buffer_t *buffer,
                            const uint8_t *src_data,
                            size_t src_size)
{
    if (buffer == nullptr || !buffer->is_allocated ||
        src_data == nullptr || src_size == 0)
    {
        return false;
    }

    if (buffer->size < src_size)
    {
        log_e("Buffer too small: need %zu, have %zu", src_size, buffer->size);
        return false;
    }

    memcpy(buffer->data, src_data, src_size);
    buffer->used = src_size;

    return true;
}

void voice_memory_get_stats(size_t *total_allocated,
                            uint8_t *buffer_count,
                            size_t *max_memory)
{
    if (total_allocated != nullptr)
    {
        *total_allocated = g_voice_memory_pool.total_allocated;
    }
    if (buffer_count != nullptr)
    {
        *buffer_count = g_voice_memory_pool.buffer_count;
    }
    if (max_memory != nullptr)
    {
        *max_memory = g_voice_memory_pool.max_memory;
    }
}

bool voice_memory_can_allocate(size_t size)
{
    if (g_voice_memory_pool.buffers == nullptr)
    {
        return false;
    }

    // Check buffer count limit
    if (g_voice_memory_pool.buffer_count >= g_voice_memory_pool.max_buffers)
    {
        return false;
    }

    // Check memory limit
    if (g_voice_memory_pool.total_allocated + size > g_voice_memory_pool.max_memory)
    {
        return false;
    }

    return true;
}

bool voice_memory_defragment(void)
{
    // Simple defragmentation: compact allocated buffers to the beginning
    if (g_voice_memory_pool.buffers == nullptr)
    {
        return false;
    }

    uint8_t write_index = 0;
    for (uint8_t read_index = 0; read_index < g_voice_memory_pool.max_buffers; read_index++)
    {
        if (g_voice_memory_pool.buffers[read_index].is_allocated)
        {
            if (write_index != read_index)
            {
                // Move buffer to earlier position
                g_voice_memory_pool.buffers[write_index] = g_voice_memory_pool.buffers[read_index];

                // Clear old position
                memset(&g_voice_memory_pool.buffers[read_index], 0, sizeof(voice_memory_buffer_t));
            }
            write_index++;
        }
    }

    return true;
}

size_t voice_memory_get_free(void)
{
    if (g_voice_memory_pool.max_memory > g_voice_memory_pool.total_allocated)
    {
        return g_voice_memory_pool.max_memory - g_voice_memory_pool.total_allocated;
    }
    return 0;
}

bool voice_memory_validate_buffer(const voice_memory_buffer_t *buffer)
{
    if (buffer == nullptr)
    {
        return false;
    }

    if (!buffer->is_allocated)
    {
        return buffer->data == nullptr && buffer->size == 0 && buffer->used == 0;
    }

    if (buffer->data == nullptr || buffer->size == 0)
    {
        return false;
    }

    if (buffer->used > buffer->size)
    {
        return false;
    }

    return true;
}
