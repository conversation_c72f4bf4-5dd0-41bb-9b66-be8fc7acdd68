#ifndef VOICE_EMBEDDED_H
#define VOICE_EMBEDDED_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// External declarations for embedded voice files
// These symbols are generated by the linker when files are embedded
extern const uint8_t voice_open_app_to_config_wav_start[] asm("_binary_voice_files_open_app_to_config_wav_start");
extern const uint8_t voice_open_app_to_config_wav_end[] asm("_binary_voice_files_open_app_to_config_wav_end");

extern const uint8_t voice_network_success_wav_start[] asm("_binary_voice_files_network_success_wav_start");
extern const uint8_t voice_network_success_wav_end[] asm("_binary_voice_files_network_success_wav_end");

extern const uint8_t voice_select_user_wav_start[] asm("_binary_voice_files_select_user_wav_start");
extern const uint8_t voice_select_user_wav_end[] asm("_binary_voice_files_select_user_wav_end");

extern const uint8_t voice_blood_pressure_data_wav_start[] asm("_binary_voice_files_blood_pressure_data_wav_start");
extern const uint8_t voice_blood_pressure_data_wav_end[] asm("_binary_voice_files_blood_pressure_data_wav_end");

extern const uint8_t voice_temperature_data_wav_start[] asm("_binary_voice_files_temperature_data_wav_start");
extern const uint8_t voice_temperature_data_wav_end[] asm("_binary_voice_files_temperature_data_wav_end");

extern const uint8_t voice_weight_data_wav_start[] asm("_binary_voice_files_weight_data_wav_start");
extern const uint8_t voice_weight_data_wav_end[] asm("_binary_voice_files_weight_data_wav_end");

extern const uint8_t voice_blood_glucose_data_wav_start[] asm("_binary_voice_files_blood_glucose_data_wav_start");
extern const uint8_t voice_blood_glucose_data_wav_end[] asm("_binary_voice_files_blood_glucose_data_wav_end");

extern const uint8_t voice_blood_oxygen_data_wav_start[] asm("_binary_voice_files_blood_oxygen_data_wav_start");
extern const uint8_t voice_blood_oxygen_data_wav_end[] asm("_binary_voice_files_blood_oxygen_data_wav_end");

extern const uint8_t voice_tap_smart_config_wav_start[] asm("_binary_voice_files_tap_smart_config_wav_start");
extern const uint8_t voice_tap_smart_config_wav_end[] asm("_binary_voice_files_tap_smart_config_wav_end");

extern const uint8_t voice_new_message_wav_start[] asm("_binary_voice_files_new_message_wav_start");
extern const uint8_t voice_new_message_wav_end[] asm("_binary_voice_files_new_message_wav_end");

/**
 * @brief Get the start pointer of an embedded voice file
 * 
 * @param index Voice file index (0-9)
 * @return const uint8_t* Pointer to start of embedded data, NULL if invalid
 */
const uint8_t* voice_embedded_get_start_ptr(uint8_t index);

/**
 * @brief Get the end pointer of an embedded voice file
 * 
 * @param index Voice file index (0-9)
 * @return const uint8_t* Pointer to end of embedded data, NULL if invalid
 */
const uint8_t* voice_embedded_get_end_ptr(uint8_t index);

/**
 * @brief Get the size of an embedded voice file
 * 
 * @param index Voice file index (0-9)
 * @return size_t Size in bytes, 0 if invalid
 */
size_t voice_embedded_get_size(uint8_t index);

/**
 * @brief Check if an embedded voice file is valid
 * 
 * @param index Voice file index (0-9)
 * @return true if valid, false otherwise
 */
bool voice_embedded_is_valid(uint8_t index);

#ifdef __cplusplus
}
#endif

#endif // VOICE_EMBEDDED_H
