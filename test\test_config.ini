[env:test_voice_module]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino

; Test configuration
build_flags = 
    -DUNIT_TEST
    -DCORE_DEBUG_LEVEL=4
    -DBOARD_HAS_PSRAM
    -DARDUINO_USB_CDC_ON_BOOT=1
    
; Include voice files for testing
board_build.embed_files = 
    voice_files/open_app_to_config.wav
    voice_files/network_success.wav
    voice_files/select_user.wav
    voice_files/blood_pressure_data.wav
    voice_files/temperature_data.wav
    voice_files/weight_data.wav
    voice_files/blood_glucose_data.wav
    voice_files/blood_oxygen_data.wav
    voice_files/tap_smart_config.wav
    voice_files/new_message.wav

; Libraries for testing
lib_deps = 
    throwtheswitch/Unity@^2.5.2
    https://github.com/schreibfaul1/ESP32-audioI2S
    
; Test source filter
test_filter = test_voice_module

; Upload and monitor settings
upload_speed = 921600
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Memory settings
board_build.partitions = huge_app.csv
board_build.arduino.memory_type = qio_opi

; Compiler settings
build_unflags = -Os
build_flags = 
    ${env.build_flags}
    -O2
    -DCONFIG_ARDUHAL_LOG_COLORS=1
