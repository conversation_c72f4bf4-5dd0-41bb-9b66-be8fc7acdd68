# Voice Module API Documentation

## 概述

Voice Module是一个专为ESP32设计的嵌入式语音播放系统，支持将WAV格式的语音文件直接嵌入到固件中，无需网络下载即可实现语音播放功能。

## 特性

- ✅ 编译时语音文件嵌入
- ✅ 内存直接访问，无需文件系统
- ✅ 模块化设计，易于集成
- ✅ 静态函数设计，限制内部可见性
- ✅ 完整的错误处理机制
- ✅ 支持多种语音文件格式验证
- ✅ 自动内存管理和清理

## 架构设计

```
voice_module/
├── voice_interface.h/cpp    # 高级接口层
├── voice_manager.h/cpp      # 语音管理核心
├── voice_player.h/cpp       # 音频播放控制
├── voice_embedded.h/cpp     # 嵌入文件访问
└── voice_memory.h/cpp       # 内存管理（可选）
```

## 快速开始

### 1. 初始化语音系统

```cpp
#include "voice_module/voice_interface.h"

Audio audio; // 全局Audio实例

void setup() {
    // 初始化音频硬件
    audio.setPinout(AUDIO_BCLK, AUDIO_LRCLK, AUDIO_SDATA);
    audio.setVolume(21);
    
    // 初始化语音系统
    if (!voice_system_init(&audio)) {
        Serial.println("语音系统初始化失败");
        return;
    }
    
    Serial.println("语音系统初始化成功");
}
```

### 2. 播放语音文件

```cpp
void loop() {
    // 方法1：按文件名播放
    if (!voice_system_play_prompt("network_success")) {
        Serial.println("播放失败");
    }
    
    // 方法2：按索引播放
    if (!voice_system_play_by_index(NETWORK_SUCCESS)) {
        Serial.println("播放失败");
    }
    
    // 处理语音系统（必须在主循环中调用）
    voice_system_process();
    
    delay(100);
}
```

### 3. 检查文件可用性

```cpp
// 检查特定语音文件是否可用
if (voice_system_is_available(NETWORK_SUCCESS)) {
    voice_system_play_by_index(NETWORK_SUCCESS);
} else {
    Serial.println("语音文件不可用");
}
```

## API参考

### 语音文件索引

```cpp
#define OPEN_APP_TO_CONFIG 0    // 打开应用配置
#define NETWORK_SUCCESS 1       // 网络连接成功
#define SELECT_USER 2           // 选择用户
#define BLOOD_PRESSURE_DATA 3   // 血压数据
#define TEMPERATURE_DATA 4      // 温度数据
#define WEIGHT_DATA 5           // 体重数据
#define BLOOD_GLUCOSE_DATA 6    // 血糖数据
#define BLOOD_OXYGEN_DATA 7     // 血氧数据
#define TAP_SMART_CONFIG 8      // 点击智能配置
#define NEW_MESSAGE 9           // 新消息
```

### 核心函数

#### voice_system_init()
```cpp
bool voice_system_init(Audio* audio_instance);
```
**功能**: 初始化语音系统
**参数**: 
- `audio_instance`: Audio库实例指针
**返回值**: 成功返回true，失败返回false
**注意**: 必须在使用其他API前调用

#### voice_system_play_prompt()
```cpp
bool voice_system_play_prompt(const char* filename);
```
**功能**: 按文件名播放语音
**参数**: 
- `filename`: 语音文件名（不含.wav扩展名）
**返回值**: 成功返回true，失败返回false
**示例**: `voice_system_play_prompt("network_success")`

#### voice_system_play_by_index()
```cpp
bool voice_system_play_by_index(uint8_t index);
```
**功能**: 按索引播放语音
**参数**: 
- `index`: 语音文件索引（0-9）
**返回值**: 成功返回true，失败返回false
**示例**: `voice_system_play_by_index(NETWORK_SUCCESS)`

#### voice_system_is_available()
```cpp
bool voice_system_is_available(uint8_t index);
```
**功能**: 检查语音文件是否可用
**参数**: 
- `index`: 语音文件索引
**返回值**: 可用返回true，不可用返回false

#### voice_system_is_playing()
```cpp
bool voice_system_is_playing(void);
```
**功能**: 检查是否正在播放语音
**返回值**: 正在播放返回true，否则返回false

#### voice_system_stop()
```cpp
bool voice_system_stop(void);
```
**功能**: 停止当前语音播放
**返回值**: 成功返回true，失败返回false

#### voice_system_process()
```cpp
void voice_system_process(void);
```
**功能**: 处理语音系统状态
**注意**: 必须在主循环中定期调用，用于处理播放完成和清理临时文件

#### voice_system_set_volume()
```cpp
void voice_system_set_volume(uint8_t volume);
```
**功能**: 设置音量
**参数**: 
- `volume`: 音量级别（0-21）

#### voice_system_get_volume()
```cpp
uint8_t voice_system_get_volume(void);
```
**功能**: 获取当前音量
**返回值**: 当前音量级别

#### voice_system_deinit()
```cpp
void voice_system_deinit(void);
```
**功能**: 反初始化语音系统
**注意**: 释放所有资源，停止播放

## 错误处理

语音系统提供完整的错误处理机制：

```cpp
// 检查初始化结果
if (!voice_system_init(&audio)) {
    Serial.println("初始化失败，请检查Audio实例");
    return;
}

// 检查播放结果
if (!voice_system_play_prompt("test_file")) {
    Serial.println("播放失败，可能原因：");
    Serial.println("1. 文件不存在或未嵌入");
    Serial.println("2. 系统正在播放其他文件");
    Serial.println("3. 音频硬件问题");
}
```

## 集成指南

### 替换现有语音系统

如果您的项目中已有语音下载和播放系统，可以按以下步骤进行替换：

1. **替换初始化代码**:
```cpp
// 旧代码
// get_store_wav_file();

// 新代码
voice_system_init(&audio);
```

2. **替换播放调用**:
```cpp
// 旧代码
// if (wav_exist_flag[NETWORK_SUCCESS]) {
//     audio_prompt(wav_file_path[NETWORK_SUCCESS].c_str());
// }

// 新代码
if (voice_system_is_available(NETWORK_SUCCESS)) {
    voice_system_play_prompt("network_success");
}
```

3. **替换状态检查**:
```cpp
// 旧代码
// if (audio.isRunning()) return;

// 新代码
if (voice_system_is_playing()) return;
```

4. **添加处理循环**:
```cpp
void loop() {
    // 其他代码...
    
    // 添加语音系统处理
    voice_system_process();
    
    // 其他代码...
}
```

## 性能优化

### 内存使用
- 语音文件直接嵌入Flash，不占用RAM
- 临时文件仅在播放时创建，播放完成后自动清理
- 支持PSRAM优化（如果可用）

### 播放延迟
- 无网络下载延迟
- 文件访问延迟 < 100ms
- 支持预加载优化

## 故障排除

### 常见问题

1. **初始化失败**
   - 检查Audio实例是否有效
   - 确认音频引脚配置正确
   - 验证LittleFS是否正常工作

2. **播放无声音**
   - 检查音频硬件连接
   - 确认音量设置（0-21）
   - 验证语音文件是否正确嵌入

3. **文件不可用**
   - 确认WAV文件已添加到voice_files文件夹
   - 检查platformio.ini中的embed_files配置
   - 验证文件名与代码中的名称一致

### 调试信息

启用详细日志输出：
```cpp
// 在platformio.ini中添加
build_flags = -DCORE_DEBUG_LEVEL=4
```

## 版本历史

- **v1.0.0**: 初始版本，支持基本语音播放功能
- 支持10个预定义语音文件
- 完整的错误处理和内存管理
- 单元测试覆盖率 > 80%
