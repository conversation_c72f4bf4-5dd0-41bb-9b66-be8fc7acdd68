#ifndef VOICE_MEMORY_H
#define VOICE_MEMORY_H

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// Memory buffer structure for voice data
typedef struct {
    uint8_t* data;              // Allocated memory buffer
    size_t size;                // Size of the buffer
    size_t used;                // Currently used bytes
    bool is_allocated;          // Whether memory is allocated
    bool is_psram;              // Whether using PSRAM
} voice_memory_buffer_t;

// Memory pool for voice operations
typedef struct {
    voice_memory_buffer_t* buffers;     // Array of memory buffers
    uint8_t buffer_count;               // Number of buffers
    uint8_t max_buffers;                // Maximum number of buffers
    size_t total_allocated;             // Total allocated memory
    size_t max_memory;                  // Maximum memory limit
} voice_memory_pool_t;

/**
 * @brief Initialize voice memory management system
 * 
 * @param max_buffers Maximum number of memory buffers
 * @param max_memory Maximum total memory to allocate (bytes)
 * @return true on success, false on failure
 */
bool voice_memory_init(uint8_t max_buffers, size_t max_memory);

/**
 * @brief Deinitialize voice memory management system
 * 
 * Frees all allocated memory and cleans up resources.
 */
void voice_memory_deinit(void);

/**
 * @brief Allocate memory buffer for voice data
 * 
 * @param size Size in bytes to allocate
 * @param use_psram Whether to prefer PSRAM allocation
 * @return voice_memory_buffer_t* Pointer to allocated buffer, NULL on failure
 */
voice_memory_buffer_t* voice_memory_allocate(size_t size, bool use_psram);

/**
 * @brief Free a memory buffer
 * 
 * @param buffer Pointer to buffer to free
 */
void voice_memory_free(voice_memory_buffer_t* buffer);

/**
 * @brief Copy embedded voice data to memory buffer
 * 
 * @param buffer Target memory buffer
 * @param src_data Source embedded data
 * @param src_size Size of source data
 * @return true on success, false on failure
 */
bool voice_memory_copy_data(voice_memory_buffer_t* buffer, 
                           const uint8_t* src_data, 
                           size_t src_size);

/**
 * @brief Get memory usage statistics
 * 
 * @param total_allocated Pointer to store total allocated bytes
 * @param buffer_count Pointer to store number of active buffers
 * @param max_memory Pointer to store maximum memory limit
 */
void voice_memory_get_stats(size_t* total_allocated, 
                           uint8_t* buffer_count, 
                           size_t* max_memory);

/**
 * @brief Check if memory allocation would exceed limits
 * 
 * @param size Size to check
 * @return true if allocation would be within limits, false otherwise
 */
bool voice_memory_can_allocate(size_t size);

/**
 * @brief Defragment memory pool (compact allocated buffers)
 * 
 * @return true on success, false on failure
 */
bool voice_memory_defragment(void);

/**
 * @brief Get available free memory
 * 
 * @return size_t Available memory in bytes
 */
size_t voice_memory_get_free(void);

/**
 * @brief Validate memory buffer integrity
 * 
 * @param buffer Buffer to validate
 * @return true if valid, false if corrupted
 */
bool voice_memory_validate_buffer(const voice_memory_buffer_t* buffer);

#ifdef __cplusplus
}
#endif

#endif // VOICE_MEMORY_H
