#include "voice_manager.h"
#include "voice_embedded.h"
#include "voice_memory.h"
#include "voice_player.h"
#include <Arduino.h>
#include <FS.h>
#include <LittleFS.h>
#include "gpio.h"

// Voice file names (must match the order in voice_file_index_t enum)
static const char *voice_file_names[VOICE_FILE_COUNT] = {
    "open_app_to_config",
    "network_success",
    "select_user",
    "blood_pressure_data",
    "temperature_data",
    "weight_data",
    "blood_glucose_data",
    "blood_oxygen_data",
    "tap_smart_config",
    "new_message"};

// Global voice file table
static voice_file_t g_voice_files[VOICE_FILE_COUNT];

// Voice manager state
static voice_status_t g_voice_status = VOICE_STATUS_UNINITIALIZED;
static Audio *g_audio_instance = nullptr;

// Forward declarations for static functions
static voice_error_t voice_manager_load_embedded_files(void);

voice_error_t voice_manager_init(void)
{
    if (g_voice_status != VOICE_STATUS_UNINITIALIZED)
    {
        return VOICE_ERROR_NONE; // Already initialized
    }

    log_i("Initializing voice manager...");

    // Load embedded voice files
    voice_error_t result = voice_manager_load_embedded_files();
    if (result != VOICE_ERROR_NONE)
    {
        return result;
    }

    g_voice_status = VOICE_STATUS_INITIALIZED;
    log_i("Voice manager initialized successfully");

    return VOICE_ERROR_NONE;
}

voice_error_t voice_manager_set_audio_instance(Audio *audio_instance)
{
    if (audio_instance == nullptr)
    {
        return VOICE_ERROR_INVALID_DATA;
    }

    g_audio_instance = audio_instance;
    log_i("Audio instance set for voice manager");

    return VOICE_ERROR_NONE;
}

void voice_manager_deinit(void)
{
    if (g_voice_status == VOICE_STATUS_UNINITIALIZED)
    {
        return;
    }

    // Stop any ongoing playback
    voice_manager_stop();

    // Reset state
    g_voice_status = VOICE_STATUS_UNINITIALIZED;
    g_audio_instance = nullptr;

    log_i("Voice manager deinitialized");
}

voice_error_t voice_manager_play(voice_file_index_t index)
{
    if (g_voice_status == VOICE_STATUS_UNINITIALIZED)
    {
        return VOICE_ERROR_NOT_INITIALIZED;
    }

    if (index >= VOICE_FILE_COUNT)
    {
        return VOICE_ERROR_INVALID_INDEX;
    }

    if (!g_voice_files[index].available)
    {
        log_w("Voice file %s not available", voice_file_names[index]);
        return VOICE_ERROR_FILE_NOT_FOUND;
    }

    if (g_voice_status == VOICE_STATUS_PLAYING)
    {
        log_w("Voice manager is busy playing another file");
        return VOICE_ERROR_AUDIO_BUSY;
    }

    // Initialize voice player if needed
    if (g_audio_instance != nullptr && !voice_player_init(g_audio_instance))
    {
        log_e("Failed to initialize voice player");
        return VOICE_ERROR_MEMORY_ERROR;
    }

    log_i("Playing voice file: %s", voice_file_names[index]);

    // Play from memory using voice player
    if (!voice_player_play_from_memory(g_voice_files[index].data,
                                       g_voice_files[index].size,
                                       voice_file_names[index]))
    {
        log_e("Failed to play voice file");
        return VOICE_ERROR_MEMORY_ERROR;
    }

    g_voice_status = VOICE_STATUS_PLAYING;
    return VOICE_ERROR_NONE;
}

voice_error_t voice_manager_play_by_name(const char *filename)
{
    if (filename == nullptr)
    {
        return VOICE_ERROR_INVALID_INDEX;
    }

    // Find matching voice file
    for (uint8_t i = 0; i < VOICE_FILE_COUNT; i++)
    {
        if (strcmp(voice_file_names[i], filename) == 0)
        {
            return voice_manager_play((voice_file_index_t)i);
        }
    }

    log_w("Voice file not found: %s", filename);
    return VOICE_ERROR_FILE_NOT_FOUND;
}

voice_error_t voice_manager_stop(void)
{
    if (g_voice_status != VOICE_STATUS_PLAYING)
    {
        return VOICE_ERROR_NONE;
    }

    voice_player_stop();
    g_voice_status = VOICE_STATUS_INITIALIZED;

    return VOICE_ERROR_NONE;
}

bool voice_manager_is_playing(void)
{
    return voice_player_is_playing();
}

voice_status_t voice_manager_get_status(void)
{
    return g_voice_status;
}

bool voice_manager_is_file_available(voice_file_index_t index)
{
    if (index >= VOICE_FILE_COUNT)
    {
        return false;
    }
    return g_voice_files[index].available;
}

const voice_file_t *voice_manager_get_file_info(voice_file_index_t index)
{
    if (index >= VOICE_FILE_COUNT)
    {
        return nullptr;
    }
    return &g_voice_files[index];
}

uint8_t voice_manager_get_file_count(void)
{
    return VOICE_FILE_COUNT;
}

const char *voice_manager_error_to_string(voice_error_t error)
{
    switch (error)
    {
    case VOICE_ERROR_NONE:
        return "No error";
    case VOICE_ERROR_NOT_INITIALIZED:
        return "Voice manager not initialized";
    case VOICE_ERROR_INVALID_INDEX:
        return "Invalid voice file index";
    case VOICE_ERROR_FILE_NOT_FOUND:
        return "Voice file not found";
    case VOICE_ERROR_MEMORY_ERROR:
        return "Memory allocation error";
    case VOICE_ERROR_AUDIO_BUSY:
        return "Audio system busy";
    case VOICE_ERROR_INVALID_DATA:
        return "Invalid voice data";
    default:
        return "Unknown error";
    }
}

// Static function implementations
static voice_error_t voice_manager_load_embedded_files(void)
{
    log_i("Loading embedded voice files...");

    for (uint8_t i = 0; i < VOICE_FILE_COUNT; i++)
    {
        g_voice_files[i].name = voice_file_names[i];
        g_voice_files[i].data = voice_embedded_get_start_ptr(i);
        g_voice_files[i].size = voice_embedded_get_size(i);
        g_voice_files[i].available = voice_embedded_is_valid(i);

        if (g_voice_files[i].available)
        {
            log_i("Loaded voice file: %s (%zu bytes)",
                  g_voice_files[i].name, g_voice_files[i].size);
        }
        else
        {
            log_w("Voice file not available: %s", g_voice_files[i].name);
        }
    }

    return VOICE_ERROR_NONE;
}
