#include "voice_interface.h"
#include "voice_manager.h"
#include "voice_player.h"
#include <Arduino.h>

// Voice file names mapping (must match the order in main.cpp)
static const char* voice_file_names[10] = {
    "open_app_to_config",    // 0 - OPEN_APP_TO_CONFIG
    "network_success",       // 1 - NETWORK_SUCCESS
    "select_user",           // 2 - SELECT_USER
    "blood_pressure_data",   // 3 - BLOOD_PRESSURE_DATA
    "temperature_data",      // 4 - TEMPERATURE_DATA
    "weight_data",           // 5 - WEIGHT_DATA
    "blood_glucose_data",    // 6 - BLOOD_GLUCOSE_DATA
    "blood_oxygen_data",     // 7 - BLOOD_OXYGEN_DATA
    "tap_smart_config",      // 8 - TAP_SMART_CONFIG
    "new_message"            // 9 - NEW_MESSAGE
};

static bool g_voice_system_initialized = false;

bool voice_system_init(Audio* audio_instance)
{
    if (g_voice_system_initialized) {
        log_w("Voice system already initialized");
        return true;
    }
    
    if (audio_instance == nullptr) {
        log_e("Audio instance is null");
        return false;
    }
    
    log_i("Initializing voice system...");
    
    // Initialize voice manager
    voice_error_t result = voice_manager_init();
    if (result != VOICE_ERROR_NONE) {
        log_e("Failed to initialize voice manager: %s", 
              voice_manager_error_to_string(result));
        return false;
    }
    
    // Set audio instance
    result = voice_manager_set_audio_instance(audio_instance);
    if (result != VOICE_ERROR_NONE) {
        log_e("Failed to set audio instance: %s", 
              voice_manager_error_to_string(result));
        voice_manager_deinit();
        return false;
    }
    
    // Initialize voice player
    if (!voice_player_init(audio_instance)) {
        log_e("Failed to initialize voice player");
        voice_manager_deinit();
        return false;
    }
    
    g_voice_system_initialized = true;
    log_i("Voice system initialized successfully");
    
    return true;
}

void voice_system_deinit(void)
{
    if (!g_voice_system_initialized) {
        return;
    }
    
    voice_player_deinit();
    voice_manager_deinit();
    g_voice_system_initialized = false;
    
    log_i("Voice system deinitialized");
}

bool voice_system_play_prompt(const char* filename)
{
    if (!g_voice_system_initialized) {
        log_e("Voice system not initialized");
        return false;
    }
    
    if (filename == nullptr) {
        log_e("Filename is null");
        return false;
    }
    
    voice_error_t result = voice_manager_play_by_name(filename);
    if (result != VOICE_ERROR_NONE) {
        log_w("Failed to play voice prompt '%s': %s", 
              filename, voice_manager_error_to_string(result));
        return false;
    }
    
    return true;
}

bool voice_system_play_by_index(uint8_t index)
{
    if (!g_voice_system_initialized) {
        log_e("Voice system not initialized");
        return false;
    }
    
    if (index >= 10) {
        log_e("Invalid voice index: %d", index);
        return false;
    }
    
    voice_error_t result = voice_manager_play((voice_file_index_t)index);
    if (result != VOICE_ERROR_NONE) {
        log_w("Failed to play voice index %d: %s", 
              index, voice_manager_error_to_string(result));
        return false;
    }
    
    return true;
}

bool voice_system_is_available(uint8_t index)
{
    if (!g_voice_system_initialized) {
        return false;
    }
    
    if (index >= 10) {
        return false;
    }
    
    return voice_manager_is_file_available((voice_file_index_t)index);
}

bool voice_system_is_playing(void)
{
    if (!g_voice_system_initialized) {
        return false;
    }
    
    return voice_manager_is_playing();
}

bool voice_system_stop(void)
{
    if (!g_voice_system_initialized) {
        return false;
    }
    
    voice_error_t result = voice_manager_stop();
    return result == VOICE_ERROR_NONE;
}

void voice_system_process(void)
{
    if (!g_voice_system_initialized) {
        return;
    }
    
    voice_player_process();
}

void voice_system_set_volume(uint8_t volume)
{
    if (!g_voice_system_initialized) {
        return;
    }
    
    voice_player_set_volume(volume);
}

uint8_t voice_system_get_volume(void)
{
    if (!g_voice_system_initialized) {
        return 0;
    }
    
    return voice_player_get_volume();
}

uint8_t voice_system_get_file_count(void)
{
    return 10; // Fixed number of voice files
}
