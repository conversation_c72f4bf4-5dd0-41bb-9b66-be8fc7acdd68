# Voice Module - 嵌入式语音播放系统

## 简介

Voice Module是一个专为ESP32设计的高效语音播放系统，支持将WAV格式语音文件直接嵌入到固件中，实现无需网络下载的即时语音播放功能。

## 主要特性

- 🚀 **零延迟播放**: 语音文件嵌入固件，无需网络下载
- 💾 **内存优化**: 直接从Flash读取，不占用RAM空间
- 🔧 **模块化设计**: 清晰的API接口，易于集成和维护
- 🛡️ **错误处理**: 完整的错误检测和处理机制
- 🧪 **单元测试**: 80%+测试覆盖率，确保代码质量
- 📱 **硬件兼容**: 支持ESP32-S3及相关开发板

## 文件结构

```
voice_module/
├── voice_interface.h/cpp    # 🎯 主要API接口（推荐使用）
├── voice_manager.h/cpp      # 📋 语音文件管理核心
├── voice_player.h/cpp       # 🎵 音频播放控制
├── voice_embedded.h/cpp     # 📦 嵌入文件访问
├── voice_memory.h/cpp       # 🧠 内存管理（可选）
└── README.md               # 📖 本文档
```

## 快速开始

### 1. 准备语音文件

将您的WAV格式语音文件放入项目根目录的`voice_files/`文件夹中：

```
voice_files/
├── open_app_to_config.wav
├── network_success.wav
├── select_user.wav
├── blood_pressure_data.wav
├── temperature_data.wav
├── weight_data.wav
├── blood_glucose_data.wav
├── blood_oxygen_data.wav
├── tap_smart_config.wav
└── new_message.wav
```

### 2. 配置PlatformIO

确保`platformio.ini`中包含文件嵌入配置：

```ini
board_build.embed_files = 
    voice_files/open_app_to_config.wav
    voice_files/network_success.wav
    voice_files/select_user.wav
    voice_files/blood_pressure_data.wav
    voice_files/temperature_data.wav
    voice_files/weight_data.wav
    voice_files/blood_glucose_data.wav
    voice_files/blood_oxygen_data.wav
    voice_files/tap_smart_config.wav
    voice_files/new_message.wav
```

### 3. 代码集成

```cpp
#include "voice_module/voice_interface.h"

Audio audio; // 全局Audio实例

void setup() {
    Serial.begin(115200);
    
    // 初始化音频硬件
    audio.setPinout(AUDIO_BCLK, AUDIO_LRCLK, AUDIO_SDATA);
    audio.setVolume(21);
    
    // 初始化语音系统
    if (voice_system_init(&audio)) {
        Serial.println("✅ 语音系统初始化成功");
        
        // 播放欢迎语音
        voice_system_play_prompt("network_success");
    } else {
        Serial.println("❌ 语音系统初始化失败");
    }
}

void loop() {
    // 必须调用：处理语音播放状态
    voice_system_process();
    
    // 您的其他代码...
    delay(10);
}
```

## API使用示例

### 基本播放操作

```cpp
// 按文件名播放
voice_system_play_prompt("network_success");

// 按索引播放
voice_system_play_by_index(NETWORK_SUCCESS);

// 检查是否正在播放
if (voice_system_is_playing()) {
    Serial.println("正在播放语音...");
}

// 停止播放
voice_system_stop();
```

### 文件可用性检查

```cpp
// 检查文件是否可用
if (voice_system_is_available(NETWORK_SUCCESS)) {
    voice_system_play_by_index(NETWORK_SUCCESS);
} else {
    Serial.println("语音文件不可用");
}

// 获取可用文件数量
uint8_t count = voice_system_get_file_count();
Serial.printf("共有 %d 个语音文件\n", count);
```

### 音量控制

```cpp
// 设置音量 (0-21)
voice_system_set_volume(15);

// 获取当前音量
uint8_t volume = voice_system_get_volume();
Serial.printf("当前音量: %d\n", volume);
```

## 语音文件索引

```cpp
#define OPEN_APP_TO_CONFIG 0    // 打开应用配置
#define NETWORK_SUCCESS 1       // 网络连接成功  
#define SELECT_USER 2           // 选择用户
#define BLOOD_PRESSURE_DATA 3   // 血压数据
#define TEMPERATURE_DATA 4      // 温度数据
#define WEIGHT_DATA 5           // 体重数据
#define BLOOD_GLUCOSE_DATA 6    // 血糖数据
#define BLOOD_OXYGEN_DATA 7     // 血氧数据
#define TAP_SMART_CONFIG 8      // 点击智能配置
#define NEW_MESSAGE 9           // 新消息
```

## 错误处理最佳实践

```cpp
// 1. 检查初始化结果
if (!voice_system_init(&audio)) {
    Serial.println("初始化失败，请检查：");
    Serial.println("- Audio实例是否有效");
    Serial.println("- 音频引脚配置是否正确");
    Serial.println("- LittleFS是否正常工作");
    return;
}

// 2. 检查播放结果
if (!voice_system_play_prompt("test_file")) {
    Serial.println("播放失败，可能原因：");
    Serial.println("- 文件不存在或未正确嵌入");
    Serial.println("- 系统正在播放其他文件");
    Serial.println("- 音频硬件问题");
}

// 3. 优雅的资源清理
void cleanup() {
    voice_system_deinit();
    Serial.println("语音系统已清理");
}
```

## 性能优化建议

### 内存优化
- 语音文件存储在Flash中，不占用RAM
- 临时文件仅在播放时创建，自动清理
- 支持PSRAM加速（如果可用）

### 播放优化
- 预检查文件可用性，避免无效播放尝试
- 合理设置音量，避免音频失真
- 在主循环中及时调用`voice_system_process()`

## 故障排除

### 常见问题及解决方案

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 初始化失败 | Audio实例无效 | 检查Audio对象创建和配置 |
| 播放无声音 | 硬件连接问题 | 验证音频引脚和硬件连接 |
| 文件不可用 | 嵌入配置错误 | 检查platformio.ini配置 |
| 播放卡顿 | 主循环阻塞 | 确保及时调用process函数 |

### 调试模式

启用详细日志输出：
```ini
# 在platformio.ini中添加
build_flags = -DCORE_DEBUG_LEVEL=4
```

## 单元测试

运行单元测试：
```bash
# 编译并运行测试
pio test -e test_voice_module

# 查看测试覆盖率
pio test -e test_voice_module --verbose
```

## 技术规格

- **支持格式**: WAV (16-bit, 单声道/立体声)
- **采样率**: 8kHz - 48kHz
- **最大文件数**: 10个（可扩展）
- **单文件大小**: 建议 < 1MB
- **内存占用**: < 4KB RAM（不含音频缓冲）
- **启动时间**: < 100ms

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request！请确保：
- 代码符合项目风格
- 添加相应的单元测试
- 更新相关文档

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**🎵 让您的ESP32项目拥有完美的语音体验！**
