#ifndef VOICE_INTERFACE_H
#define VOICE_INTERFACE_H

#include <Arduino.h>
#include <Audio.h>

#ifdef __cplusplus
extern "C" {
#endif

// Voice file indices that match the original system
#define OPEN_APP_TO_CONFIG 0
#define NETWORK_SUCCESS 1
#define SELECT_USER 2
#define BLOOD_PRESSURE_DATA 3
#define TEMPERATURE_DATA 4
#define WEIGHT_DATA 5
#define BLOOD_GLUCOSE_DATA 6
#define BLOOD_OXYGEN_DATA 7
#define TAP_SMART_CONFIG 8
#define NEW_MESSAGE 9

/**
 * @brief Initialize the voice system
 * 
 * This function replaces the original voice file checking and downloading.
 * It initializes the embedded voice files for immediate use.
 * 
 * @param audio_instance Pointer to the global Audio instance
 * @return true on success, false on failure
 */
bool voice_system_init(Audio* audio_instance);

/**
 * @brief Deinitialize the voice system
 */
void voice_system_deinit(void);

/**
 * @brief Play a voice prompt by filename
 * 
 * This function replaces the original audio_prompt function.
 * It plays voice files directly from embedded memory.
 * 
 * @param filename Voice filename (without .wav extension)
 * @return true on success, false on failure
 */
bool voice_system_play_prompt(const char* filename);

/**
 * @brief Play a voice prompt by index
 * 
 * @param index Voice file index (0-9)
 * @return true on success, false on failure
 */
bool voice_system_play_by_index(uint8_t index);

/**
 * @brief Check if a voice file is available
 * 
 * This function replaces the wav_exist_flag array checks.
 * 
 * @param index Voice file index (0-9)
 * @return true if available, false otherwise
 */
bool voice_system_is_available(uint8_t index);

/**
 * @brief Check if voice system is currently playing
 * 
 * @return true if playing, false otherwise
 */
bool voice_system_is_playing(void);

/**
 * @brief Stop current voice playback
 * 
 * @return true on success, false on failure
 */
bool voice_system_stop(void);

/**
 * @brief Process voice system (call in main loop)
 * 
 * This function should be called regularly to handle playback completion.
 */
void voice_system_process(void);

/**
 * @brief Set voice volume
 * 
 * @param volume Volume level (0-21)
 */
void voice_system_set_volume(uint8_t volume);

/**
 * @brief Get current volume
 * 
 * @return uint8_t Current volume level
 */
uint8_t voice_system_get_volume(void);

/**
 * @brief Get voice file count
 * 
 * @return uint8_t Number of voice files
 */
uint8_t voice_system_get_file_count(void);

#ifdef __cplusplus
}
#endif

#endif // VOICE_INTERFACE_H
