#include "voice_embedded.h"
#include <Arduino.h>

// Static array to hold pointers to embedded voice file data
static const uint8_t* voice_start_ptrs[10] = {
    voice_open_app_to_config_wav_start,
    voice_network_success_wav_start,
    voice_select_user_wav_start,
    voice_blood_pressure_data_wav_start,
    voice_temperature_data_wav_start,
    voice_weight_data_wav_start,
    voice_blood_glucose_data_wav_start,
    voice_blood_oxygen_data_wav_start,
    voice_tap_smart_config_wav_start,
    voice_new_message_wav_start
};

static const uint8_t* voice_end_ptrs[10] = {
    voice_open_app_to_config_wav_end,
    voice_network_success_wav_end,
    voice_select_user_wav_end,
    voice_blood_pressure_data_wav_end,
    voice_temperature_data_wav_end,
    voice_weight_data_wav_end,
    voice_blood_glucose_data_wav_end,
    voice_blood_oxygen_data_wav_end,
    voice_tap_smart_config_wav_end,
    voice_new_message_wav_end
};

const uint8_t* voice_embedded_get_start_ptr(uint8_t index)
{
    if (index >= 10) {
        return nullptr;
    }
    return voice_start_ptrs[index];
}

const uint8_t* voice_embedded_get_end_ptr(uint8_t index)
{
    if (index >= 10) {
        return nullptr;
    }
    return voice_end_ptrs[index];
}

size_t voice_embedded_get_size(uint8_t index)
{
    if (index >= 10) {
        return 0;
    }
    
    const uint8_t* start = voice_start_ptrs[index];
    const uint8_t* end = voice_end_ptrs[index];
    
    if (start == nullptr || end == nullptr || end <= start) {
        return 0;
    }
    
    return (size_t)(end - start);
}

bool voice_embedded_is_valid(uint8_t index)
{
    if (index >= 10) {
        return false;
    }
    
    const uint8_t* start = voice_start_ptrs[index];
    const uint8_t* end = voice_end_ptrs[index];
    
    // Check if pointers are valid and size is reasonable
    if (start == nullptr || end == nullptr || end <= start) {
        return false;
    }
    
    size_t size = (size_t)(end - start);
    
    // Basic sanity check: WAV files should be at least 44 bytes (header size)
    // and not larger than 1MB for voice prompts
    if (size < 44 || size > (1024 * 1024)) {
        return false;
    }
    
    // Check if it looks like a WAV file by examining the header
    if (size >= 12) {
        // Check for "RIFF" signature at the beginning
        if (start[0] == 'R' && start[1] == 'I' && start[2] == 'F' && start[3] == 'F') {
            // Check for "WAVE" format identifier
            if (start[8] == 'W' && start[9] == 'A' && start[10] == 'V' && start[11] == 'E') {
                return true;
            }
        }
    }
    
    return false;
}
